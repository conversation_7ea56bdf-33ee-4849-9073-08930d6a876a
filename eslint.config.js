import js from '@eslint/js'
import tseslint from 'typescript-eslint'

import stylistic from '@stylistic/eslint-plugin'
import react from 'eslint-plugin-react'
import prettier from 'eslint-plugin-prettier'
// import unusedImports from 'eslint-plugin-unused-imports'
import pluginQuery from '@tanstack/eslint-plugin-query'

export default tseslint.config([
  {
    files: ['packages/**/*.{ts,tsx}'],
  },

  js.configs.recommended,
  ...tseslint.configs.recommended,

  {
    ignores: [
      'packages/**/dist',
      'electron-builder.mjs',
      'packages/renderer/packages/overlay-renderer/releases'
    ],
    plugins: {
      react,
      // 'react-hooks': reactHooks,
      prettier,
      // 'unused-imports': unusedImports,
      // 'react-refresh': reactRefresh,
      '@stylistic': stylistic,
      '@tanstack/query': pluginQuery,
    },
    rules: {
      '@stylistic/arrow-parens': ['error', 'as-needed'],
      '@stylistic/indent': [
        'error', 2, {
          ignoredNodes: [
            'FunctionExpression > .params[decorators.length > 0]',
            'FunctionExpression > .params > :matches(Decorator, :not(:first-child))',
            'ClassBody.body > PropertyDefinition[decorators.length > 0] > .key',
          ],
          SwitchCase: 1,
        },
      ],
      '@stylistic/jsx-quotes': ['error', 'prefer-double'],
      '@stylistic/key-spacing': ['error', { beforeColon: false, afterColon: true, mode: 'strict' }],
      '@stylistic/keyword-spacing': ['error', { before: true, after: true }],
      '@stylistic/linebreak-style': ['off'],
      '@stylistic/no-multiple-empty-lines': ['error', { max: 1 }],
      '@stylistic/max-len': ['off'],
      '@stylistic/object-curly-newline': ['error', { consistent: true }],
      '@stylistic/object-curly-spacing': ['error', 'always'],
      '@stylistic/quotes': ['error', 'single'],
      '@stylistic/padded-blocks': ['error', { classes: 'start', blocks: 'never' }],
      '@stylistic/semi': ['error', 'never'],
      '@stylistic/type-annotation-spacing': ['error', {
        before: false,
        after: true,
        overrides: { arrow: { before: true, after: true } },
      }],

      '@tanstack/query/exhaustive-deps': 'error',

      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-namespace': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-unused-vars': ['off'],
      '@typescript-eslint/no-use-before-define': ['off'],
      '@typescript-eslint/no-var-requires': 'off',

      'arrow-body-style': ['off'],
      'arrow-spacing': ['error', { before: true, after: true }],
      'comma-spacing': ['error'],
      'eqeqeq': ['error'],
      'import/no-unresolved': ['off'],
      'no-async-promise-executor': 'off',
      'no-console': ['off'],
      'no-inner-declarations': 'off',
      'no-multiple-empty-lines': ['error', { max: 2 }],
      'no-param-reassign': ['off'],
      'no-return-await': ['off'],
      'no-unused-vars': ['off'],
      'no-use-before-define': ['off'],
      'no-debugger': 'warn',
      'no-duplicate-imports': 'error',

      'react/jsx-closing-bracket-location': 'error',
      'react/jsx-closing-tag-location': 'error',
      'react/jsx-curly-brace-presence': ['error', 'never'],
      'react/jsx-curly-newline': 'error',
      'react/jsx-filename-extension': ['off'],
      'react/jsx-indent': ['error', 2],
      'react/jsx-indent-props': ['error', 2],
      'react/jsx-max-props-per-line': ['error', {
        maximum: 1,
        when: 'multiline',
      }],
      'react/jsx-space-before-closing': 'error',
      'react/prop-types': ['off'],
      'react/react-in-jsx-scope': 'off',
      'react/self-closing-comp': ['error'],

      // 'react-hooks/exhaustive-deps': 'off',

      'space-before-blocks': 'error',
      'space-infix-ops': 'error',

      // 'unused-imports/no-unused-imports': 'error',
      // 'unused-imports/no-unused-vars': [
      //   'warn', {
      //     vars: 'all',
      //     varsIgnorePattern: 'res|next|^err|_',
      //     args: 'after-used',
      //     argsIgnorePattern: 'res|next|^err|_',
      //   },
      // ],
    },
  },
])
