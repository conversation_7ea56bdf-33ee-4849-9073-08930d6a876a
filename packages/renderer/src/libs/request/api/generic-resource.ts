import request, { fetchPagination, fetchPaginationGet, requestCurrying } from '../request'
import { PaginationParams } from '@app/shared/types/database.types'
import {
  CommonCategory,
  FontResource,
  MaterialResource,
  PasterResource,
  ResourceSource,
  SoundResource,
  StyledTextResource,
  TimbreResource,
  UploadLocal
} from '@/types/resources'
import { OssModule } from '@/libs/request/api/oss'
import { ResourceTypes } from '@app/shared/types/resource-cache.types'

export namespace GenericResourceModule {
  
  export interface ResourceConfigMap {
    paster: {
      resource: PasterResource.Paster
      listParams: PaginationParams & { categoryIds?: string[] }
      category: CommonCategory[]
    }
    music: {
      resource: SoundResource.Sound
      listParams: PaginationParams
      category: CommonCategory[]
    }
    voice: {
      resource: SoundResource.Sound
      listParams: PaginationParams & { categoryIds?: string[] }
      category: CommonCategory[]
    }
    font: {
      resource: FontResource.Font
      listParams: PaginationParams
      category: CommonCategory[]
    }
    fontStyle: {
      resource: StyledTextResource.StyledText
      listParams: PaginationParams
      category: never
    }
    timbre: {
      resource: TimbreResource.Timbre
      listParams: PaginationParams
      category: never
    }
    media: {
      resource: MaterialResource.Media
      listParams: MaterialResource.MaterialMediaParams
      category: never
    }
    directory: {
      resource: MaterialResource.Directory
      listParams: MaterialResource.MaterialDirectoryParams
      category: never
    }
  }

  export const endpoints = {
    // ===== Library 列表 =====
    list<K extends ResourceTypes>(kind: K, params: ResourceConfigMap[K]['listParams']) {
      return fetchPagination<ResourceConfigMap[K]['resource']>(`/app-api/creative/library/${kind}/search`, params as any)
    },
    category<K extends ResourceTypes>(kind: K) {
      type CategoryType = ResourceConfigMap[K]['category']
      return request.get<CategoryType>(`/app-api/creative/library/${kind}/category`)
    },
    collected<K extends ResourceTypes>(kind: K, params: PaginationParams) {
      return fetchPagination<ResourceConfigMap[K]['resource']>(
        `/app-api/creative/library/${kind}/collect-page`,
        params
      )
    },

    // ===== 团队资源 =====
    teamList<K extends ResourceTypes>(kind: K, params: PaginationParams & { folderUuid: string }) {
      return requestCurrying.post<typeof params>(`/app-api/creative/team/library/${kind}/page`)(params)
    },
    teamCreate<K extends ResourceTypes>(kind: K, payload: UploadLocal) {
      return requestCurrying.post<UploadLocal>(`/app-api/creative/team/library/${kind}/create`)(payload)
    },
    teamRename<K extends ResourceTypes>(kind: K, payload: { fileId: string; fileName: string }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/library/${kind}/rename`)(payload)
    },
    teamDelete<K extends ResourceTypes>(kind: K, payload: { fileIds: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/library/${kind}/delete`)(payload)
    },
    teamMove<K extends ResourceTypes>(kind: K, payload: { fileIds: string[]; folderUuid: string }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/library/${kind}/move`)(payload)
    },

    // ===== 文件夹操作 =====
    dirList<K extends ResourceTypes>(kind: K) {
      return request.get<MaterialResource.Directory[]>(`/app-api/creative/team/directory/${kind}/list`)
    },
    dirCreate<K extends ResourceTypes>(kind: K, payload: Pick<MaterialResource.Directory, 'parentId' | 'folderName'>) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/create`)(payload)
    },
    dirRename<K extends ResourceTypes>(kind: K, payload: Pick<MaterialResource.Directory, 'folderId' | 'folderName'>) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/rename`)(payload)
    },
    dirDelete<K extends ResourceTypes>(kind: K, payload: { folderIds: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/delete`)(payload)
    },
    dirMove<K extends ResourceTypes>(kind: K, payload: { folderIds: string[]; parentId: string }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/team/directory/${kind}/move`)(payload)
    },

    // ===== 回收站 =====
    recycle<K extends ResourceTypes>(kind: K, payload: { fileIds?: string[]; folderIds?: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/storage/${kind}/recycle`)(payload)
    },
    recycleList<K extends ResourceTypes>(kind: K, params?: any) {
      return fetchPaginationGet<ResourceConfigMap[K]['resource']>(`/app-api/creative/storage/${kind}/recycle-page`)(params)
    },
    back<K extends ResourceTypes>(kind: K, payload: { fileIds?: string[]; folderIds?: string[] }) {
      return requestCurrying.post<typeof payload>(`/app-api/creative/storage/${kind}/back`)(payload)
    },

    cover(objectId: string) {
      return OssModule.getObjectHref(objectId)
    }
  }

  //TODO: 临时处理
  export const ResourceTypeByResourceSource: Record<string, ResourceTypes> = {

    [ResourceSource.LOCAL_STICK_MULTI_SELECT]: 'paster',
    [ResourceSource.LOCAL_MUSIC_MULTI_SELECT]: 'music',
    [ResourceSource.LOCAL_SOUND_MULTI_SELECT]: 'voice',

    [ResourceSource.LOCAL_STICK]: 'paster',
    [ResourceSource.LOCAL_MUSIC]: 'music',
    [ResourceSource.LOCAL_SOUND]: 'voice',

    [ResourceSource.LOCAL_STICK_FOLDER]: 'paster',
    [ResourceSource.LOCAL_MUSIC_FOLDER]: 'music',
    [ResourceSource.LOCAL_SOUND_FOLDER]: 'voice',
  }
}
