import React, { useCallback, useMemo, useRef, useState } from 'react'
import { MaterialResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { useResource } from '@/modules/video-editor/hooks/resource/useResource'
import { ObjectHrefBasedImage } from '@/components/ObjectHrefBasedImage'
import { parseUrlFromObjectHref } from '@/libs/tools/resource'
import { clamp } from 'lodash'

// 去掉签名参数，仅用于缓存 key
const getUnsignedUrlKey = (url: string): string => {
  try {
    const u = new URL(url)
    return `${u.origin}${u.pathname}`
  } catch {
    return url
  }
}

interface MediaPreviewProps {
  media: MaterialResource.Media
  isEditItem: boolean
  orientation: string
  defaultWidth: number
}

// 瓦片图中每一行最多展示 10 个关键帧
const MAX_FRAMES_PER_ROW = 10

export const MediaPreview: React.FC<MediaPreviewProps> = ({ media, defaultWidth }) => {
  const originalRatio = media.width / media.height
  const defaultHeight = defaultWidth / originalRatio

  const { preloadImage } = useResource()

  const [hoverFrame, setHoverFrame] = useState(0)
  const [totalFrames, setTotalFrames] = useState<number>(0)
  const [frameHeight, setFrameHeight] = useState<number>(defaultHeight)
  const [frameWidth, setFrameWidth] = useState<number>(defaultWidth)
  const [naturalWidth, setNaturalWidth] = useState<number>(0)
  const [naturalHeight, setNaturalHeight] = useState<number>(0)
  const [coverFrameUrl, setCoverFrameUrl] = useState<string | null>(null)

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const loadedImageRef = useRef<HTMLImageElement | null>(null)

  // 请求瓦片图并解析尺寸
  const fetchCoverFrame = async (retry = true) => {
    if (!media.coverFrame) return

    try {
      const href = await parseUrlFromObjectHref(media.coverFrame)
      if (!href) return
      setCoverFrameUrl(href)

      // 文件名示例：d310401c-84ad-4fb3-a3ce-e14da89a090f_216x384_1_tile_00001.jpg
      const match = href.match(/_(\d+)x(\d+)_(\d+)_/)
      if (!match || match.length < 4) return

      const frameWidth = Number(match[1])
      const frameHeight = Number(match[2])
      const frames = Number(match[3])
      const rows = Math.ceil(frames / MAX_FRAMES_PER_ROW)

      const img = loadedImageRef.current || (
        await preloadImage(getUnsignedUrlKey(href))
          .catch(async error => {
            if (retry) {
              console.warn('签名可能失效，尝试重新获取 signed URL')
              await fetchCoverFrame(false) // 只重试一次
            } else {
              console.error('图片加载失败，已放弃重试:', error)
            }
            return null
          })
      )

      if (!img) return

      loadedImageRef.current = img

      img.src = href
      img.onload = () => {
        if (!containerRef.current) return
        const { width: containerWidth, height: containerHeight } = containerRef.current.getBoundingClientRect()

        const isVertical = frameHeight > frameWidth
        let displayFrameWidth: number
        let displayFrameHeight: number

        if (isVertical) {
          // 竖屏：固定高度，宽度按比例缩放
          displayFrameHeight = containerHeight
          displayFrameWidth = displayFrameHeight * (frameWidth / frameHeight)
        } else {
          // 横屏：固定宽度，高度按比例缩放
          displayFrameWidth = containerWidth
          displayFrameHeight = displayFrameWidth * (frameHeight / frameWidth)
        }

        const naturalWidths = displayFrameWidth * MAX_FRAMES_PER_ROW
        const naturalHeights = displayFrameHeight * rows

        setNaturalWidth(naturalWidths)
        setNaturalHeight(naturalHeights)

        // 一帧显示的宽度和高度
        setFrameWidth(displayFrameWidth)
        setFrameHeight(displayFrameHeight)

        // Ensure frameCount is updated correctly
        setTotalFrames(frames)
      }
    } catch (err) {
      console.error('获取瓦片图失败:', err)
    }
  }

  // 鼠标移动时计算当前帧
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!containerRef.current || !coverFrameUrl || totalFrames === 0) return

    // 获取容器的位置信息
    const { left, width } = containerRef.current.getBoundingClientRect()
    const relativeX = e.clientX - left

    // 计算相对位置的比例
    const percent = relativeX / width
    const frameIndex = clamp(Math.floor(percent * totalFrames), 0, totalFrames - 1)
    // console.log(`相对位置: ${relativeX}, 容器宽度: ${width}, 总帧数: ${frameCount}, 当前帧索引: ${frameIndex}`)

    if (frameIndex !== hoverFrame) {
      setHoverFrame(frameIndex)
    }
  }

  // 鼠标进入容器时加载瓦片图
  const handleMouseEnter = useCallback(() => {
    // 如果已经有定时器在运行，先清除掉
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      if (!coverFrameUrl) {
        fetchCoverFrame()
      }
    }, loadedImageRef.current ? 0 : 300)
  }, [coverFrameUrl])

  // 鼠标离开容器时重置帧索引
  const handleMouseLeave = () => {
    setHoverFrame(0)
    setCoverFrameUrl(null)
    setFrameHeight(defaultHeight)
    setFrameWidth(defaultWidth)

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current) // 清除延迟加载的定时器
    }
  }

  // 根据行列计算背景偏移量
  const backgroundPosition = useMemo(() => {
    const row = Math.floor(hoverFrame / MAX_FRAMES_PER_ROW) // 计算当前帧的行号
    const col = hoverFrame % MAX_FRAMES_PER_ROW // 计算当前帧的列号

    return -col * frameWidth - row * frameHeight
  }, [hoverFrame, frameWidth, frameHeight])

  return (
    <div
      ref={containerRef}
      className={cn('flex relative group h-full w-full items-center justify-center')}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
    >
      <div
        className="relative overflow-hidden rounded"
        style={{ height: frameHeight, width: frameWidth }} // 设置容器的高度
      >
        {coverFrameUrl ? (
          <div
            className="w-full h-full bg-no-repeat"
            style={{
              width: `${naturalWidth}px`,
              height: `${naturalHeight}px`,
              backgroundImage: `url(${coverFrameUrl})`,
              backgroundSize: '100% 100%',
              backgroundPosition,
            }}
          />
        ) : (
          <ObjectHrefBasedImage src={media.cover} alt={media.fileName} className="w-full h-full" />
        )}
      </div>

      {coverFrameUrl && (
        <div
          className="absolute border-gray-500 border-r border-dashed"
          style={{
            left: `${hoverFrame / totalFrames * 100}%`,
            height: '100%',
            width: 1,
          }}
        />
      )}
    </div>
  )
}

