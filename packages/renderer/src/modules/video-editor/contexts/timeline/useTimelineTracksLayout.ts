import { useCallback, useMemo, useState } from 'react'
import {
  NARRATOR_TIMELINE_TRACK_HEIGHT,
  TIMELINE_GRID_GAP_PX,
  TIMELINE_TRACK_HEIGHT
} from '@/modules/video-editor/constants'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { TrackType } from '@/modules/video-editor/types'

export type TimelineTracksLayout = {
  layout: Array<{ height: number, top: number }>
  totalHeight: number
  rowGap: number

  getTrackHeight(trackIndex: number): number
}

export const useTimelineTracksLayout = (): TimelineTracksLayout => {
  const { tracks } = useEditorContext()

  const [rowGap] = useState(TIMELINE_GRID_GAP_PX)

  const { layout, totalHeight } = useMemo(() => {
    let totalHeight = 0

    const layout: Array<{ height: number, top: number }> = []

    for (const track of tracks) {
      const height = track.type === TrackType.NARRATION
        ? NARRATOR_TIMELINE_TRACK_HEIGHT
        : TIMELINE_TRACK_HEIGHT

      layout.push({
        height,
        top: totalHeight
      })
      totalHeight += height + rowGap
    }

    return { layout, totalHeight }
  }, [tracks, rowGap])

  const getTrackHeight = useCallback<TimelineTracksLayout['getTrackHeight']>(
    trackIndex => {
      return layout[trackIndex]?.height || TIMELINE_TRACK_HEIGHT
    },
    [layout]
  )

  return {
    layout,
    rowGap,
    totalHeight,
    getTrackHeight,
  }
}
