import { PlayerMetadata, RenderRequestPayload } from '@app/shared/types/ipc/mixcut'
import { Track, TrackType } from '@/modules/video-editor/types'
import { GeneratedMixcut } from '@/modules/mixcut/context/context'
import { calculateRenderableOverlays } from '@/modules/video-editor/utils/overlay-helper'
import { removeAllLocalUrlOfOverlay } from '@/modules/video-editor/utils/track-helper'

export function generateRenderRequestPayload(
  tracks: Track[],
  mixcut: GeneratedMixcut,
  playerMetadata: PlayerMetadata
): RenderRequestPayload {
  const overlays = calculateRenderableOverlays(removeAllLocalUrlOfOverlay(tracks), {
    [TrackType.VIDEO]: mixcut.videoCombo.selections,
    [TrackType.NARRATION]: mixcut.narrationSelections,
  }, true, false)

  return {
    // TODO: 动态获取
    id: 'V0-0-1',
    inputProps: {
      overlays,
      playerMetadata
    }
  }
}
